@echo off
REM ============================================================================
REM Simple CCS Build Script for MSPM0 Project
REM ============================================================================
REM Quick and easy build script for the bq769x2 TIDA010247 project
REM ============================================================================

echo Building CCS Project...

REM Set project variables
set PROJECT=bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang
set WORKSPACE=%~dp0

REM Try common CCS installation paths
set CCS_PATH=
if exist "C:\ti\ccs2020\ccs\eclipse\ccs-serverc.exe" set CCS_PATH=C:\ti\ccs2020\ccs\eclipse\ccs-serverc.exe

if "%CCS_PATH%"=="" (
    echo ERROR: Code Composer Studio not found!
    echo Please install CCS or update the paths in this script.
    pause
    exit /b 1
)

echo Using CCS at: %CCS_PATH%
echo Project: %PROJECT%
echo Workspace: %WORKSPACE%

REM Build the project
echo.
echo Starting build...
"%CCS_PATH%" -noSplash -data "%WORKSPACE%" -application com.ti.ccstudio.apps.projectBuild -ccs.projects %PROJECT% -ccs.buildType incremental

if %ERRORLEVEL% equ 0 (
    echo.
    echo *** BUILD SUCCESSFUL ***
    echo Output: Debug\%PROJECT%.out
) else (
    echo.
    echo *** BUILD FAILED ***
    echo Error code: %ERRORLEVEL%
    pause
)
