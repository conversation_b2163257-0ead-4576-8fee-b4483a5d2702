# CCS Command Line Build Instructions

This document explains how to build the `bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang` project using command line tools.

## Prerequisites

1. **Code Composer Studio (CCS)** - CCS Eclipse or CCS Theia must be installed
2. **TI MSPM0 SDK** - Required for the project dependencies
3. **Windows Command Prompt** - These scripts are designed for Windows

## CCS Version Support

The build scripts support both:
- **CCS Theia** (recommended) - Modern web-based IDE using `ccs-server-cli`
- **CCS Eclipse** (legacy) - Traditional Eclipse-based IDE using `eclipsec`

## Build Scripts

Four batch files have been created for different use cases:

### 1. `build_simple.bat` - Quick Build
The simplest option for basic builds.

```cmd
# Incremental build
build_simple.bat
```

**Features:**
- Automatic CCS detection
- Simple incremental build
- Minimal configuration required

### 2. `build_project.bat` - Standard Build (Universal)
Recommended for most users. Supports both CCS Eclipse and CCS Theia.

```cmd
# Incremental build
build_project.bat

# Clean build artifacts
build_project.bat clean

# Full rebuild (clean + build)
build_project.bat rebuild

# Show help
build_project.bat help
```

**Features:**
- Automatic detection of CCS Eclipse or CCS Theia
- Clean, rebuild, and incremental build options
- Error checking and detailed output
- Help documentation
- Universal compatibility

### 3. `build_theia.bat` - CCS Theia Optimized
Specifically optimized for CCS Theia with advanced options.

```cmd
# Basic build
build_theia.bat

# Build specific configuration
build_theia.bat --config Release

# Clean build
build_theia.bat --clean

# Full rebuild
build_theia.bat --rebuild

# Show help
build_theia.bat --help
```

**Features:**
- Optimized for CCS Theia command line interface
- Advanced configuration options
- Detailed build status reporting
- Automatic CCS Theia detection

### 4. `build_advanced.bat` - Advanced Build
Full-featured build script with extensive options.

```cmd
# Basic build
build_advanced.bat

# Build specific configuration
build_advanced.bat --config Release

# Clean build
build_advanced.bat --clean

# Full rebuild with error details
build_advanced.bat --rebuild --errors

# Show help
build_advanced.bat --help
```

**Features:**
- Multiple build configurations (Debug, Release)
- Detailed error reporting
- Auto-open projects option
- Comprehensive command line options
- Works with both CCS Eclipse and CCS Theia

## Build Configurations

The project supports different build configurations:

- **Debug** (default) - Includes debug symbols and optimizations disabled
- **Release** - Optimized build for production

## Output Files

After a successful build, you'll find:

- **Executable**: `Debug/bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang.out`
- **Map file**: `Debug/bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang.map`
- **Object files**: Various `.obj` files in the Debug directory

## Troubleshooting

### CCS Not Found
If you get "CCS not found" errors:

1. **For CCS Theia**, check if installed in:
   - `C:\ti\ccstheia1230\ccs\eclipse\ccs-server-cli.exe`
   - `C:\ti\ccstheia1220\ccs\eclipse\ccs-server-cli.exe`
   - `C:\ti\ccstheia1210\ccs\eclipse\ccs-server-cli.exe`
   - `C:\ti\ccstheia1200\ccs\eclipse\ccs-server-cli.exe`

2. **For CCS Eclipse**, check if installed in:
   - `C:\ti\ccs2020\ccs\eclipse\eclipsec.exe`
   - `C:\ti\ccs1220\ccs\eclipse\eclipsec.exe`
   - `C:\ti\ccs1210\ccs\eclipse\eclipsec.exe`
   - `C:\ti\ccs1200\ccs\eclipse\eclipsec.exe`

3. If CCS is installed elsewhere, modify the paths in the build scripts

### Build Failures
Common solutions:

1. **Clean the project first**:
   ```cmd
   build_project.bat clean
   build_project.bat
   ```

2. **Check SDK installation**: Ensure TI MSPM0 SDK is properly installed

3. **Verify workspace**: Make sure you're running the script from the project root directory

4. **Check project configuration**: Ensure the `.project` and `.cproject` files are present

### SysConfig Issues
This project uses TI SysConfig. If you encounter SysConfig-related errors:

1. Ensure SysConfig is installed with CCS
2. Check that the `.syscfg` file is present in the project
3. Verify SysConfig tool paths in CCS installation

## Manual CCS Command Line

If you prefer to use CCS command line directly:

### CCS Theia Commands (Recommended)

```cmd
# Basic build command
"C:\ti\ccstheia1230\ccs\eclipse\ccs-server-cli.exe" -noSplash -workspace "." -application com.ti.ccs.apps.buildProject -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang

# Clean build
"C:\ti\ccstheia1230\ccs\eclipse\ccs-server-cli.exe" -noSplash -workspace "." -application com.ti.ccs.apps.buildProject -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang -ccs.clean

# Build specific configuration
"C:\ti\ccstheia1230\ccs\eclipse\ccs-server-cli.exe" -noSplash -workspace "." -application com.ti.ccs.apps.buildProject -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang -ccs.configuration Release
```

### CCS Eclipse Commands (Legacy)

```cmd
# Basic build command
"C:\ti\ccs2020\ccs\eclipse\eclipsec.exe" -noSplash -data "." -application com.ti.ccstudio.apps.projectBuild -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang -ccs.buildType incremental

# Clean build
"C:\ti\ccs2020\ccs\eclipse\eclipsec.exe" -noSplash -data "." -application com.ti.ccstudio.apps.projectBuild -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang -ccs.buildType clean

# Full rebuild
"C:\ti\ccs2020\ccs\eclipse\eclipsec.exe" -noSplash -data "." -application com.ti.ccstudio.apps.projectBuild -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang -ccs.buildType full
```

## Integration with CI/CD

These scripts can be integrated into continuous integration systems:

```cmd
# Example CI build command
call build_project.bat rebuild
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
```

## Additional Resources

- [TI CCS Command Line Documentation](https://software-dl.ti.com/ccs/esd/documents/ccs_projects-command-line.html)
- [MSPM0 SDK Documentation](https://www.ti.com/tool/MSPM0-SDK)
- [BQ769x2 Reference Design](https://www.ti.com/tool/TIDA-010247)

## Key Differences: CCS Theia vs CCS Eclipse

| Feature | CCS Theia | CCS Eclipse |
|---------|-----------|-------------|
| Executable | `ccs-server-cli.exe` | `eclipsec.exe` |
| Workspace Parameter | `-workspace` | `-data` |
| Application | `com.ti.ccs.apps.buildProject` | `com.ti.ccstudio.apps.projectBuild` |
| Clean Command | `-ccs.clean` | `-ccs.buildType clean` |
| Build Types | Incremental (default) | `-ccs.buildType incremental/full/clean` |

## Notes

- These scripts are designed for Windows environments
- For Linux/macOS, replace `.exe` with appropriate executables and adjust paths accordingly
- CCS Theia is the modern, recommended version
- CCS Eclipse is legacy but still supported
- The project uses TI Clang compiler toolchain
- SysConfig files (`.syscfg`) are automatically processed during build
- Both CCS versions support the same project files and configurations
