# CCS Command Line Build Instructions

This document explains how to build the `bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang` project using command line tools.

## Prerequisites

1. **Code Composer Studio (CCS)** - Must be installed on your system
2. **TI MSPM0 SDK** - Required for the project dependencies
3. **Windows Command Prompt** - These scripts are designed for Windows

## Build Scripts

Three batch files have been created for different use cases:

### 1. `build_simple.bat` - Quick Build
The simplest option for basic builds.

```cmd
# Incremental build
build_simple.bat
```

**Features:**
- Automatic CCS detection
- Simple incremental build
- Minimal configuration required

### 2. `build_project.bat` - Standard Build
Recommended for most users with basic options.

```cmd
# Incremental build
build_project.bat

# Clean build artifacts
build_project.bat clean

# Full rebuild (clean + build)
build_project.bat rebuild

# Show help
build_project.bat help
```

**Features:**
- Clean, rebuild, and incremental build options
- Error checking and detailed output
- Help documentation

### 3. `build_advanced.bat` - Advanced Build
Full-featured build script with extensive options.

```cmd
# Basic build
build_advanced.bat

# Build specific configuration
build_advanced.bat --config Release

# Clean build
build_advanced.bat --clean

# Full rebuild with error details
build_advanced.bat --rebuild --errors

# Show help
build_advanced.bat --help
```

**Features:**
- Multiple build configurations (Debug, Release)
- Detailed error reporting
- Auto-open projects option
- Comprehensive command line options

## Build Configurations

The project supports different build configurations:

- **Debug** (default) - Includes debug symbols and optimizations disabled
- **Release** - Optimized build for production

## Output Files

After a successful build, you'll find:

- **Executable**: `Debug/bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang.out`
- **Map file**: `Debug/bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang.map`
- **Object files**: Various `.obj` files in the Debug directory

## Troubleshooting

### CCS Not Found
If you get "CCS not found" errors:

1. Check if CCS is installed in one of these locations:
   - `C:\ti\ccs2020\ccs`
   - `C:\ti\ccs1220\ccs`
   - `C:\ti\ccs1210\ccs`
   - `C:\ti\ccs1200\ccs`

2. If CCS is installed elsewhere, modify the `CCS_INSTALL_DIR` variable in the build scripts

### Build Failures
Common solutions:

1. **Clean the project first**:
   ```cmd
   build_project.bat clean
   build_project.bat
   ```

2. **Check SDK installation**: Ensure TI MSPM0 SDK is properly installed

3. **Verify workspace**: Make sure you're running the script from the project root directory

4. **Check project configuration**: Ensure the `.project` and `.cproject` files are present

### SysConfig Issues
This project uses TI SysConfig. If you encounter SysConfig-related errors:

1. Ensure SysConfig is installed with CCS
2. Check that the `.syscfg` file is present in the project
3. Verify SysConfig tool paths in CCS installation

## Manual CCS Command Line

If you prefer to use CCS command line directly:

```cmd
# Basic build command
"C:\ti\ccs2020\ccs\eclipse\eclipsec.exe" -noSplash -data "." -application com.ti.ccstudio.apps.projectBuild -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang -ccs.buildType incremental

# Clean build
"C:\ti\ccs2020\ccs\eclipse\eclipsec.exe" -noSplash -data "." -application com.ti.ccstudio.apps.projectBuild -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang -ccs.buildType clean

# Full rebuild
"C:\ti\ccs2020\ccs\eclipse\eclipsec.exe" -noSplash -data "." -application com.ti.ccstudio.apps.projectBuild -ccs.projects bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang -ccs.buildType full
```

## Integration with CI/CD

These scripts can be integrated into continuous integration systems:

```cmd
# Example CI build command
call build_project.bat rebuild
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
```

## Additional Resources

- [TI CCS Command Line Documentation](https://software-dl.ti.com/ccs/esd/documents/ccs_projects-command-line.html)
- [MSPM0 SDK Documentation](https://www.ti.com/tool/MSPM0-SDK)
- [BQ769x2 Reference Design](https://www.ti.com/tool/TIDA-010247)

## Notes

- These scripts are designed for Windows environments
- For Linux/macOS, replace `eclipsec.exe` with `eclipse` and adjust paths accordingly
- The project uses TI Clang compiler toolchain
- SysConfig files (`.syscfg`) are automatically processed during build
