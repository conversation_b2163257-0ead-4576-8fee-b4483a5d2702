@echo off
REM ============================================================================
REM CCS Project Build Script for bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang
REM ============================================================================
REM This batch file builds the CCS project using command line tools
REM 
REM Prerequisites:
REM - Code Composer Studio (CCS) must be installed
REM - TI MSPM0 SDK must be installed
REM - Project workspace must be properly configured
REM
REM Usage: build_project.bat [clean|rebuild|help]
REM   clean   - Clean the project
REM   rebuild - Clean and build the project
REM   help    - Show this help message
REM   (no args) - Incremental build
REM ============================================================================

setlocal enabledelayedexpansion

REM Configuration Variables
set PROJECT_NAME=bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang
set WORKSPACE_DIR=%~dp0
set BUILD_CONFIG=Debug

REM Default CCS installation paths (modify if needed)
set CCS_INSTALL_DIR=C:\ti\ccs2020\ccs
set ECLIPSEC_PATH=%CCS_INSTALL_DIR%\eclipse\eclipsec.exe

REM Check if CCS is installed
if not exist "%ECLIPSEC_PATH%" (
    echo ERROR: Code Composer Studio not found at %ECLIPSEC_PATH%
    echo Please modify the CCS_INSTALL_DIR variable in this script to point to your CCS installation
    echo Common CCS installation paths:
    echo   C:\ti\ccs1200\ccs
    echo   C:\ti\ccs1210\ccs
    echo   C:\ti\ccs1220\ccs
    echo   C:\ti\ccs2020\ccs
    pause
    exit /b 1
)

REM Parse command line arguments
set BUILD_TYPE=incremental
if "%1"=="clean" set BUILD_TYPE=clean
if "%1"=="rebuild" set BUILD_TYPE=full
if "%1"=="help" goto :show_help
if "%1"=="-h" goto :show_help
if "%1"=="--help" goto :show_help

echo ============================================================================
echo Building CCS Project: %PROJECT_NAME%
echo Workspace: %WORKSPACE_DIR%
echo Build Configuration: %BUILD_CONFIG%
echo Build Type: %BUILD_TYPE%
echo CCS Path: %ECLIPSEC_PATH%
echo ============================================================================

REM Build the project
echo Starting build...
"%ECLIPSEC_PATH%" -noSplash -data "%WORKSPACE_DIR%" -application com.ti.ccstudio.apps.projectBuild -ccs.projects %PROJECT_NAME% -ccs.configuration %BUILD_CONFIG% -ccs.buildType %BUILD_TYPE% -ccs.listProblems

REM Check build result
if %ERRORLEVEL% equ 0 (
    echo.
    echo ============================================================================
    echo BUILD SUCCESSFUL
    echo ============================================================================
    echo Output file: %WORKSPACE_DIR%\%BUILD_CONFIG%\%PROJECT_NAME%.out
    echo Map file: %WORKSPACE_DIR%\%BUILD_CONFIG%\%PROJECT_NAME%.map
    echo ============================================================================
) else (
    echo.
    echo ============================================================================
    echo BUILD FAILED
    echo ============================================================================
    echo Error code: %ERRORLEVEL%
    echo Check the output above for error details
    echo ============================================================================
    pause
    exit /b %ERRORLEVEL%
)

goto :end

:show_help
echo ============================================================================
echo CCS Project Build Script Help
echo ============================================================================
echo.
echo Usage: build_project.bat [command]
echo.
echo Commands:
echo   (no args)  - Perform incremental build
echo   clean      - Clean the project (remove build artifacts)
echo   rebuild    - Clean and rebuild the project (full build)
echo   help       - Show this help message
echo.
echo Configuration:
echo   Project: %PROJECT_NAME%
echo   Build Config: %BUILD_CONFIG%
echo   Workspace: %WORKSPACE_DIR%
echo.
echo Notes:
echo   - Ensure Code Composer Studio is installed
echo   - Modify CCS_INSTALL_DIR variable if CCS is installed in a different location
echo   - The script will automatically detect and use the project configuration
echo.
echo ============================================================================
goto :end

:end
endlocal
