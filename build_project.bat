@echo off
REM ============================================================================
REM CCS Project Build Script for bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang
REM ============================================================================
REM This batch file builds the CCS project using command line tools
REM Supports both CCS Eclipse and CCS Theia
REM
REM Prerequisites:
REM - Code Composer Studio (CCS Eclipse or CCS Theia) must be installed
REM - TI MSPM0 SDK must be installed
REM - Project workspace must be properly configured
REM
REM Usage: build_project.bat [clean|rebuild|help]
REM   clean   - Clean the project
REM   rebuild - Clean and build the project
REM   help    - Show this help message
REM   (no args) - Incremental build
REM ============================================================================

setlocal enabledelayedexpansion

REM Configuration Variables
set PROJECT_NAME=bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang
set WORKSPACE_DIR=%~dp0
set BUILD_CONFIG=Debug

REM Auto-detect CCS installation (Theia or Eclipse)
set CCS_TYPE=
set CCS_EXECUTABLE=
set CCS_INSTALL_DIR=

REM Check for CCS Theia first (newer)
for %%d in (C:\ti\ccstheia1200 C:\ti\ccstheia1210 C:\ti\ccstheia1220 C:\ti\ccstheia1230) do (
    if exist "%%d\ccs\eclipse\ccs-server-cli.exe" (
        set CCS_INSTALL_DIR=%%d
        set CCS_EXECUTABLE=%%d\ccs\eclipse\ccs-server-cli.exe
        set CCS_TYPE=THEIA
        goto :found_ccs
    )
)

REM Check for CCS Eclipse (legacy)
for %%d in (C:\ti\ccs2020\ccs C:\ti\ccs1220\ccs C:\ti\ccs1210\ccs C:\ti\ccs1200\ccs) do (
    if exist "%%d\eclipse\eclipsec.exe" (
        set CCS_INSTALL_DIR=%%d
        set CCS_EXECUTABLE=%%d\eclipse\eclipsec.exe
        set CCS_TYPE=ECLIPSE
        goto :found_ccs
    )
)

echo ERROR: Code Composer Studio not found!
echo Please install CCS or modify the paths in this script.
echo.
echo Searched for CCS Theia in:
echo   C:\ti\ccstheia1200\ccs\eclipse\ccs-server-cli.exe
echo   C:\ti\ccstheia1210\ccs\eclipse\ccs-server-cli.exe
echo   C:\ti\ccstheia1220\ccs\eclipse\ccs-server-cli.exe
echo   C:\ti\ccstheia1230\ccs\eclipse\ccs-server-cli.exe
echo.
echo Searched for CCS Eclipse in:
echo   C:\ti\ccs2020\ccs\eclipse\eclipsec.exe
echo   C:\ti\ccs1220\ccs\eclipse\eclipsec.exe
echo   C:\ti\ccs1210\ccs\eclipse\eclipsec.exe
echo   C:\ti\ccs1200\ccs\eclipse\eclipsec.exe
pause
exit /b 1

:found_ccs

REM Parse command line arguments
set BUILD_TYPE=incremental
set BUILD_OPTION=
if "%1"=="clean" (
    set BUILD_TYPE=clean
    set BUILD_OPTION=-ccs.clean
)
if "%1"=="rebuild" (
    set BUILD_TYPE=full
    set BUILD_OPTION=
)
if "%1"=="help" goto :show_help
if "%1"=="-h" goto :show_help
if "%1"=="--help" goto :show_help

echo ============================================================================
echo Building CCS Project: %PROJECT_NAME%
echo Workspace: %WORKSPACE_DIR%
echo Build Configuration: %BUILD_CONFIG%
echo Build Type: %BUILD_TYPE%
echo CCS Type: %CCS_TYPE%
echo CCS Path: %CCS_EXECUTABLE%
echo ============================================================================

REM Build the project with appropriate command for CCS type
echo Starting build...
if "%CCS_TYPE%"=="THEIA" (
    REM CCS Theia command
    if "%BUILD_TYPE%"=="clean" (
        "%CCS_EXECUTABLE%" -noSplash -workspace "%WORKSPACE_DIR%" -application com.ti.ccs.apps.buildProject -ccs.projects %PROJECT_NAME% -ccs.configuration %BUILD_CONFIG% -ccs.clean
    ) else (
        "%CCS_EXECUTABLE%" -noSplash -workspace "%WORKSPACE_DIR%" -application com.ti.ccs.apps.buildProject -ccs.projects %PROJECT_NAME% -ccs.configuration %BUILD_CONFIG%
    )
) else (
    REM CCS Eclipse command
    "%CCS_EXECUTABLE%" -noSplash -data "%WORKSPACE_DIR%" -application com.ti.ccstudio.apps.projectBuild -ccs.projects %PROJECT_NAME% -ccs.configuration %BUILD_CONFIG% -ccs.buildType %BUILD_TYPE% -ccs.listProblems
)

REM Check build result
if %ERRORLEVEL% equ 0 (
    echo.
    echo ============================================================================
    echo BUILD SUCCESSFUL
    echo ============================================================================
    echo Output file: %WORKSPACE_DIR%\%BUILD_CONFIG%\%PROJECT_NAME%.out
    echo Map file: %WORKSPACE_DIR%\%BUILD_CONFIG%\%PROJECT_NAME%.map
    echo ============================================================================
) else (
    echo.
    echo ============================================================================
    echo BUILD FAILED
    echo ============================================================================
    echo Error code: %ERRORLEVEL%
    echo Check the output above for error details
    echo ============================================================================
    pause
    exit /b %ERRORLEVEL%
)

goto :end

:show_help
echo ============================================================================
echo CCS Project Build Script Help
echo ============================================================================
echo.
echo Usage: build_project.bat [command]
echo.
echo Commands:
echo   (no args)  - Perform incremental build
echo   clean      - Clean the project (remove build artifacts)
echo   rebuild    - Clean and rebuild the project (full build)
echo   help       - Show this help message
echo.
echo Configuration:
echo   Project: %PROJECT_NAME%
echo   Build Config: %BUILD_CONFIG%
echo   Workspace: %WORKSPACE_DIR%
echo.
echo Supported CCS Versions:
echo   - CCS Theia (recommended): ccstheia1200, ccstheia1210, ccstheia1220, ccstheia1230
echo   - CCS Eclipse (legacy): ccs1200, ccs1210, ccs1220, ccs2020
echo.
echo Notes:
echo   - Script automatically detects CCS Theia or CCS Eclipse
echo   - CCS Theia uses 'ccs-server-cli' command line interface
echo   - CCS Eclipse uses 'eclipsec' command line interface
echo   - The script will automatically detect and use the project configuration
echo.
echo ============================================================================
goto :end

:end
endlocal
