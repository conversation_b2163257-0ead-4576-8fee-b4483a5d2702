@echo off
REM ============================================================================
REM Advanced CCS Project Build Script
REM ============================================================================
REM This script provides advanced build options for the CCS project
REM Supports multiple build configurations and additional features
REM ============================================================================

setlocal enabledelayedexpansion

REM Configuration Variables
set PROJECT_NAME=bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang
set WORKSPACE_DIR=%~dp0
set DEFAULT_CONFIG=Debug

REM CCS Installation Detection
set CCS_INSTALL_DIR=
for %%d in (C:\ti\ccs1200\ccs C:\ti\ccs1210\ccs C:\ti\ccs1220\ccs C:\ti\ccs2020\ccs) do (
    if exist "%%d\eclipse\eclipsec.exe" (
        set CCS_INSTALL_DIR=%%d
        goto :found_ccs
    )
)

echo ERROR: Code Composer Studio not found in common installation directories
echo Please install CCS or modify this script to point to your CCS installation
pause
exit /b 1

:found_ccs
set ECLIPSEC_PATH=%CCS_INSTALL_DIR%\eclipse\eclipsec.exe

REM Parse command line arguments
set BUILD_CONFIG=%DEFAULT_CONFIG%
set BUILD_TYPE=incremental
set SHOW_ERRORS=false
set AUTO_OPEN=false

:parse_args
if "%1"=="" goto :start_build
if "%1"=="--config" (
    set BUILD_CONFIG=%2
    shift
    shift
    goto :parse_args
)
if "%1"=="-c" (
    set BUILD_CONFIG=%2
    shift
    shift
    goto :parse_args
)
if "%1"=="--clean" (
    set BUILD_TYPE=clean
    shift
    goto :parse_args
)
if "%1"=="--rebuild" (
    set BUILD_TYPE=full
    shift
    goto :parse_args
)
if "%1"=="--errors" (
    set SHOW_ERRORS=true
    shift
    goto :parse_args
)
if "%1"=="--auto-open" (
    set AUTO_OPEN=true
    shift
    goto :parse_args
)
if "%1"=="--help" goto :show_help
if "%1"=="-h" goto :show_help
if "%1"=="help" goto :show_help

REM Unknown argument
echo ERROR: Unknown argument: %1
echo Use --help for usage information
exit /b 1

:start_build
echo ============================================================================
echo Advanced CCS Project Build
echo ============================================================================
echo Project: %PROJECT_NAME%
echo Workspace: %WORKSPACE_DIR%
echo Build Configuration: %BUILD_CONFIG%
echo Build Type: %BUILD_TYPE%
echo CCS Installation: %CCS_INSTALL_DIR%
echo Show Errors: %SHOW_ERRORS%
echo Auto Open Projects: %AUTO_OPEN%
echo ============================================================================

REM Prepare build command
set BUILD_CMD="%ECLIPSEC_PATH%" -noSplash -data "%WORKSPACE_DIR%" -application com.ti.ccstudio.apps.projectBuild -ccs.projects %PROJECT_NAME% -ccs.configuration %BUILD_CONFIG% -ccs.buildType %BUILD_TYPE%

if "%SHOW_ERRORS%"=="true" (
    set BUILD_CMD=!BUILD_CMD! -ccs.listProblems
)

if "%AUTO_OPEN%"=="true" (
    set BUILD_CMD=!BUILD_CMD! -ccs.autoOpen
)

echo Executing build command...
echo Command: !BUILD_CMD!
echo.

REM Execute the build
!BUILD_CMD!

REM Check build result and provide detailed feedback
if %ERRORLEVEL% equ 0 (
    echo.
    echo ============================================================================
    echo BUILD SUCCESSFUL
    echo ============================================================================
    echo Configuration: %BUILD_CONFIG%
    echo Output Directory: %WORKSPACE_DIR%\%BUILD_CONFIG%
    
    REM Check if output files exist
    if exist "%WORKSPACE_DIR%\%BUILD_CONFIG%\%PROJECT_NAME%.out" (
        echo Output File: %PROJECT_NAME%.out [FOUND]
    ) else (
        echo Output File: %PROJECT_NAME%.out [NOT FOUND]
    )
    
    if exist "%WORKSPACE_DIR%\%BUILD_CONFIG%\%PROJECT_NAME%.map" (
        echo Map File: %PROJECT_NAME%.map [FOUND]
    ) else (
        echo Map File: %PROJECT_NAME%.map [NOT FOUND]
    )
    
    echo ============================================================================
    echo Build completed successfully at %date% %time%
    echo ============================================================================
) else (
    echo.
    echo ============================================================================
    echo BUILD FAILED
    echo ============================================================================
    echo Error Code: %ERRORLEVEL%
    echo Configuration: %BUILD_CONFIG%
    echo.
    echo Troubleshooting Tips:
    echo 1. Check that all required SDKs are installed
    echo 2. Verify project configuration is correct
    echo 3. Ensure workspace is not corrupted
    echo 4. Try cleaning the project first: build_advanced.bat --clean
    echo 5. Check CCS installation and paths
    echo ============================================================================
    pause
    exit /b %ERRORLEVEL%
)

goto :end

:show_help
echo ============================================================================
echo Advanced CCS Project Build Script Help
echo ============================================================================
echo.
echo Usage: build_advanced.bat [options]
echo.
echo Options:
echo   --config ^<name^>    Build configuration (Debug, Release, etc.)
echo   -c ^<name^>          Short form of --config
echo   --clean            Clean the project
echo   --rebuild          Clean and rebuild (full build)
echo   --errors           Show detailed error information
echo   --auto-open        Automatically open closed projects
echo   --help, -h         Show this help message
echo.
echo Examples:
echo   build_advanced.bat                    # Incremental build with Debug config
echo   build_advanced.bat --config Release  # Build Release configuration
echo   build_advanced.bat --rebuild         # Full rebuild
echo   build_advanced.bat --clean           # Clean project
echo   build_advanced.bat --errors          # Show detailed errors
echo.
echo Current Configuration:
echo   Project: %PROJECT_NAME%
echo   Default Config: %DEFAULT_CONFIG%
echo   Workspace: %WORKSPACE_DIR%
echo   CCS Path: %CCS_INSTALL_DIR%
echo.
echo ============================================================================

:end
endlocal
