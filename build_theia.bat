@echo off
REM ============================================================================
REM CCS Theia Build Script for bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang
REM ============================================================================
REM This batch file builds the CCS project using CCS Theia command line tools
REM Optimized specifically for CCS Theia (ccs-server-cli)
REM 
REM Prerequisites:
REM - Code Composer Studio Theia must be installed
REM - TI MSPM0 SDK must be installed
REM - Project workspace must be properly configured
REM
REM Usage: build_theia.bat [options]
REM   --clean        - Clean the project
REM   --rebuild      - Clean and rebuild the project
REM   --config <cfg> - Specify build configuration (Debug, Release)
REM   --help         - Show this help message
REM   (no args)      - Incremental build
REM ============================================================================

setlocal enabledelayedexpansion

REM Configuration Variables
set PROJECT_NAME=bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang
set WORKSPACE_DIR=%~dp0
set BUILD_CONFIG=Debug
set BUILD_ACTION=build
set CLEAN_FIRST=false

REM Auto-detect CCS Theia installation
set CCS_THEIA_CLI=
for %%d in (C:\ti\ccstheia1230 C:\ti\ccstheia1220 C:\ti\ccstheia1210 C:\ti\ccstheia1200) do (
    if exist "%%d\ccs\eclipse\ccs-server-cli.exe" (
        set CCS_THEIA_CLI=%%d\ccs\eclipse\ccs-server-cli.exe
        set CCS_INSTALL_DIR=%%d
        goto :found_theia
    )
)

echo ERROR: CCS Theia not found!
echo Please install CCS Theia or verify installation paths.
echo.
echo Searched for CCS Theia in:
echo   C:\ti\ccstheia1230\ccs\eclipse\ccs-server-cli.exe
echo   C:\ti\ccstheia1220\ccs\eclipse\ccs-server-cli.exe
echo   C:\ti\ccstheia1210\ccs\eclipse\ccs-server-cli.exe
echo   C:\ti\ccstheia1200\ccs\eclipse\ccs-server-cli.exe
echo.
echo If CCS Theia is installed elsewhere, modify this script accordingly.
pause
exit /b 1

:found_theia

REM Parse command line arguments
:parse_args
if "%1"=="" goto :start_build
if "%1"=="--clean" (
    set BUILD_ACTION=clean
    shift
    goto :parse_args
)
if "%1"=="--rebuild" (
    set CLEAN_FIRST=true
    set BUILD_ACTION=build
    shift
    goto :parse_args
)
if "%1"=="--config" (
    set BUILD_CONFIG=%2
    shift
    shift
    goto :parse_args
)
if "%1"=="--help" goto :show_help
if "%1"=="-h" goto :show_help
if "%1"=="help" goto :show_help

REM Unknown argument
echo ERROR: Unknown argument: %1
echo Use --help for usage information
exit /b 1

:start_build
echo ============================================================================
echo CCS Theia Project Build
echo ============================================================================
echo Project: %PROJECT_NAME%
echo Workspace: %WORKSPACE_DIR%
echo Build Configuration: %BUILD_CONFIG%
echo Build Action: %BUILD_ACTION%
echo Clean First: %CLEAN_FIRST%
echo CCS Theia CLI: %CCS_THEIA_CLI%
echo CCS Installation: %CCS_INSTALL_DIR%
echo ============================================================================

REM Execute clean if requested for rebuild
if "%CLEAN_FIRST%"=="true" (
    echo.
    echo Cleaning project first...
    "%CCS_THEIA_CLI%" -noSplash -workspace "%WORKSPACE_DIR%" -application com.ti.ccs.apps.buildProject -ccs.projects %PROJECT_NAME% -ccs.configuration %BUILD_CONFIG% -ccs.clean
    
    if !ERRORLEVEL! neq 0 (
        echo ERROR: Clean operation failed with error code !ERRORLEVEL!
        pause
        exit /b !ERRORLEVEL!
    )
    echo Clean completed successfully.
    echo.
)

REM Execute the main build action
echo Starting %BUILD_ACTION% operation...

if "%BUILD_ACTION%"=="clean" (
    "%CCS_THEIA_CLI%" -noSplash -workspace "%WORKSPACE_DIR%" -application com.ti.ccs.apps.buildProject -ccs.projects %PROJECT_NAME% -ccs.configuration %BUILD_CONFIG% -ccs.clean
) else (
    "%CCS_THEIA_CLI%" -noSplash -workspace "%WORKSPACE_DIR%" -application com.ti.ccs.apps.buildProject -ccs.projects %PROJECT_NAME% -ccs.configuration %BUILD_CONFIG%
)

REM Check build result
if %ERRORLEVEL% equ 0 (
    echo.
    echo ============================================================================
    echo BUILD SUCCESSFUL
    echo ============================================================================
    echo Configuration: %BUILD_CONFIG%
    echo Action: %BUILD_ACTION%
    
    if "%BUILD_ACTION%"=="build" (
        echo Output Directory: %WORKSPACE_DIR%\%BUILD_CONFIG%
        
        REM Check if output files exist
        if exist "%WORKSPACE_DIR%\%BUILD_CONFIG%\%PROJECT_NAME%.out" (
            echo Output File: %PROJECT_NAME%.out [FOUND]
            for %%F in ("%WORKSPACE_DIR%\%BUILD_CONFIG%\%PROJECT_NAME%.out") do echo File Size: %%~zF bytes
        ) else (
            echo Output File: %PROJECT_NAME%.out [NOT FOUND]
        )
        
        if exist "%WORKSPACE_DIR%\%BUILD_CONFIG%\%PROJECT_NAME%.map" (
            echo Map File: %PROJECT_NAME%.map [FOUND]
        ) else (
            echo Map File: %PROJECT_NAME%.map [NOT FOUND]
        )
    )
    
    echo ============================================================================
    echo Build completed successfully at %date% %time%
    echo ============================================================================
) else (
    echo.
    echo ============================================================================
    echo BUILD FAILED
    echo ============================================================================
    echo Error Code: %ERRORLEVEL%
    echo Configuration: %BUILD_CONFIG%
    echo Action: %BUILD_ACTION%
    echo.
    echo Troubleshooting Tips:
    echo 1. Check that TI MSPM0 SDK is properly installed
    echo 2. Verify project configuration matches your setup
    echo 3. Ensure workspace is not corrupted
    echo 4. Try cleaning first: build_theia.bat --clean
    echo 5. Check CCS Theia installation and SDK paths
    echo 6. Verify SysConfig tool is available
    echo ============================================================================
    pause
    exit /b %ERRORLEVEL%
)

goto :end

:show_help
echo ============================================================================
echo CCS Theia Project Build Script Help
echo ============================================================================
echo.
echo Usage: build_theia.bat [options]
echo.
echo Options:
echo   --clean            Clean the project (remove build artifacts)
echo   --rebuild          Clean and rebuild the project
echo   --config ^<name^>    Build configuration (Debug, Release, etc.)
echo   --help, -h         Show this help message
echo.
echo Examples:
echo   build_theia.bat                    # Incremental build with Debug config
echo   build_theia.bat --config Release  # Build Release configuration
echo   build_theia.bat --rebuild          # Clean and rebuild
echo   build_theia.bat --clean            # Clean project only
echo.
echo Current Configuration:
echo   Project: %PROJECT_NAME%
echo   Default Config: %BUILD_CONFIG%
echo   Workspace: %WORKSPACE_DIR%
echo   CCS Theia CLI: %CCS_THEIA_CLI%
echo.
echo Notes:
echo   - This script is optimized for CCS Theia
echo   - Uses 'ccs-server-cli' command line interface
echo   - Automatically detects CCS Theia installation
echo   - Supports SysConfig and TI MSPM0 SDK
echo.
echo ============================================================================

:end
endlocal
